package http

import (
	"log/slog"
	"net/http"
	"time"
	"ziaacademy-backend/internal/models"
	"ziaacademy-backend/internal/token"

	"github.com/gin-gonic/gin"
)

// Login godoc
//
//		@Summary		Login
//		@Description	login with email and password
//	 @Param			item	body	models.Credentials	true	"user_email and password"
//		@Tags			login
//		@Accept			json
//		@Produce		json
//		@Success		200	{object}	models.LoginResponse
//		@Failure		400	{object}	HTTPError
//		@Failure		404	{object}	HTTPError
//		@Failure		500	{object}	HTTPError
//		@Router			/login [post]
//
// Login is the HTTP handler to login to the application
func (h *Handlers) Login(ctx *gin.Context) {
	start := time.Now()
	clientIP := ctx.ClientIP()

	creds := new(models.Credentials)
	if err := ctx.ShouldBindJSON(creds); err != nil {
		duration := time.Since(start)
		slog.Warn("Login failed - invalid request body",
			"client_ip", clientIP,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	slog.Info("Login attempt",
		"email", creds.UserEmail,
		"client_ip", clientIP,
	)

	// Check if the username exists and password matches
	userID, err := h.db.ValidateUserPassword(ctx, creds.UserEmail, creds.Password)
	if err != nil {
		duration := time.Since(start)
		slog.Warn("Login failed - invalid credentials",
			"email", creds.UserEmail,
			"client_ip", clientIP,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid credentials"})
		return
	}

	// Get user details to determine role
	user, err := h.db.GetUserByID(ctx.Request.Context(), userID)
	if err != nil {
		duration := time.Since(start)
		slog.Error("Login failed - failed to get user details",
			"email", creds.UserEmail,
			"user_id", userID,
			"client_ip", clientIP,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user details"})
		return
	}

	// Generate JWT token
	token, err := token.GenerateJWT(userID)
	if err != nil {
		duration := time.Since(start)
		slog.Error("Login failed - token generation error",
			"email", creds.UserEmail,
			"user_id", userID,
			"client_ip", clientIP,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate token"})
		return
	}

	duration := time.Since(start)
	slog.Info("Login successful",
		"email", creds.UserEmail,
		"user_id", userID,
		"role", user.Role,
		"client_ip", clientIP,
		"duration_ms", duration.Milliseconds(),
	)

	// Return appropriate response based on user role
	if user.Role == "Student" {
		// Get student details
		student, err := h.db.GetStudentForGetByUserID(ctx.Request.Context(), userID)
		if err != nil {
			slog.Error("Login failed - failed to get student details",
				"email", creds.UserEmail,
				"user_id", userID,
				"client_ip", clientIP,
				"error", err.Error(),
			)
			ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get student details"})
			return
		}

		response := models.StudentLoginResponse{
			Token:   token,
			Student: *student,
			Role:    user.Role,
		}
		ctx.JSON(http.StatusOK, response)
	} else {
		// For admin or other roles, return UserForGet
		userForGet := models.UserForGet{
			ID:          user.ID,
			FullName:    user.FullName,
			Email:       user.Email,
			PhoneNumber: user.PhoneNumber,
			CreatedAt:   user.CreatedAt,
		}

		response := models.AdminLoginResponse{
			Token: token,
			User:  userForGet,
			Role:  user.Role,
		}
		ctx.JSON(http.StatusOK, response)
	}
}
