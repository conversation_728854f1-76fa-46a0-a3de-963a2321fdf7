package db

import (
	"context"
	"fmt"
	"log/slog"
	"time"
	"ziaacademy-backend/internal/models"
	"ziaacademy-backend/internal/token"

	"golang.org/x/crypto/bcrypt"
)

func (p *DbPlugin) CreateStudent(ctx context.Context, student *models.Student) (*models.Student, error) {
	start := time.Now()
	slog.Info("Creating student", "email", student.User.Email)

	// Start a database transaction to ensure atomicity
	tx := p.db.Begin()
	if tx.Error != nil {
		duration := time.Since(start)
		slog.Error("Failed to begin transaction for student creation",
			"email", student.User.Email,
			"error", tx.Error.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, tx.Error
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Hash the password before storing
	if student.User.PasswordHash != "" {
		combined := student.User.PasswordHash + token.SecretKeyStr
		hash, err := bcrypt.GenerateFromPassword([]byte(combined), bcrypt.DefaultCost)
		if err != nil {
			tx.Rollback()
			duration := time.Since(start)
			slog.Error("Failed to hash student password",
				"email", student.User.Email,
				"error", err.Error(),
				"duration_ms", duration.Milliseconds(),
			)
			return nil, err
		}
		student.User.PasswordHash = string(hash)
	}

	// Create the user first
	if err := tx.Create(&student.User).Error; err != nil {
		tx.Rollback()
		duration := time.Since(start)
		slog.Error("Failed to create user for student",
			"email", student.User.Email,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, err
	}

	// Set the UserID for the student
	student.UserID = student.User.ID

	// Create the student record
	if err := tx.Create(student).Error; err != nil {
		tx.Rollback()
		duration := time.Since(start)
		slog.Error("Failed to create student",
			"email", student.User.Email,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, err
	}

	// Commit the transaction
	if err := tx.Commit().Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to commit student creation transaction",
			"email", student.User.Email,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, err
	}

	// Load the complete student with user association
	if err := p.db.Preload("User").First(student, student.ID).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to load created student",
			"student_id", student.ID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, err
	}

	duration := time.Since(start)
	slog.Info("Student created successfully",
		"email", student.User.Email,
		"student_id", student.ID,
		"duration_ms", duration.Milliseconds(),
	)
	return student, nil
}

// GetStudentIDByUserID gets the student ID for a given user ID
func (p *DbPlugin) GetStudentIDByUserID(ctx context.Context, userID uint) (uint, error) {
	start := time.Now()
	slog.Debug("Getting student ID by user ID", "user_id", userID)

	var student models.Student
	if err := p.db.Where("user_id = ?", userID).First(&student).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to get student ID by user ID",
			"user_id", userID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return 0, err
	}

	duration := time.Since(start)
	slog.Debug("Student ID retrieved successfully",
		"user_id", userID,
		"student_id", student.ID,
		"duration_ms", duration.Milliseconds(),
	)

	return student.ID, nil
}

func (p *DbPlugin) EnrollStudentInCourse(ctx context.Context, userID,
	courseID uint) (*models.Student, error) {
	start := time.Now()
	slog.Info("Enrolling student in course",
		"user_id", userID,
		"course_id", courseID,
	)

	var student models.Student
	var course models.Course

	// Retrieve student with existing courses
	if err := p.db.Preload("User").Preload("Courses").
		Where("user_id = ?", userID).First(&student).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to retrieve student for enrollment",
			"user_id", userID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, err
	}

	// Retrieve course details
	if err := p.db.
		Where("id = ?", courseID).First(&course).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to retrieve course for enrollment",
			"course_id", courseID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, err
	}

	// Check if student is already enrolled in this course
	for _, enrolledCourse := range student.Courses {
		if enrolledCourse.ID == courseID {
			slog.Info("Student already enrolled in course",
				"user_id", userID,
				"course_id", courseID,
				"course_name", course.Name,
			)
			return &student, nil
		}
	}

	// For paid courses, check if student has a completed transaction
	if !course.IsFree {
		slog.Info("Checking transaction for paid course enrollment",
			"user_id", userID,
			"course_id", courseID,
			"course_name", course.Name,
		)

		// Get completed transactions for this student
		completedTransactions, err := p.GetCompletedTransactionsByStudent(ctx, student.ID)
		if err != nil {
			duration := time.Since(start)
			slog.Error("Failed to retrieve completed transactions for enrollment",
				"student_id", student.ID,
				"course_id", courseID,
				"error", err.Error(),
				"duration_ms", duration.Milliseconds(),
			)
			return nil, err
		}

		// Check if any completed transaction contains this course
		hasValidTransaction := false
		for _, transaction := range completedTransactions {
			for _, transactionCourse := range transaction.Courses {
				if transactionCourse.ID == courseID {
					hasValidTransaction = true
					slog.Info("Found valid transaction for course enrollment",
						"transaction_id", transaction.ID,
						"student_id", student.ID,
						"course_id", courseID,
					)
					break
				}
			}
			if hasValidTransaction {
				break
			}
		}

		if !hasValidTransaction {
			duration := time.Since(start)
			slog.Error("No valid transaction found for paid course enrollment",
				"student_id", student.ID,
				"course_id", courseID,
				"course_name", course.Name,
				"duration_ms", duration.Milliseconds(),
			)
			return nil, fmt.Errorf("enrollment denied: no valid transaction found for paid course '%s'", course.Name)
		}
	}

	// Enroll student in the course
	if err := p.db.Model(&student).Association("Courses").Append(&course); err != nil {
		duration := time.Since(start)
		slog.Error("Failed to enroll student in course",
			"user_id", userID,
			"course_id", courseID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, err
	}

	duration := time.Since(start)
	slog.Info("Student enrolled in course successfully",
		"user_id", userID,
		"course_id", courseID,
		"course_name", course.Name,
		"student_email", student.User.Email,
		"is_free_course", course.IsFree,
		"duration_ms", duration.Milliseconds(),
	)
	return &student, nil
}

func (p *DbPlugin) GetStudentByUserID(ctx context.Context,
	userID uint) (*models.StudentForCreate, error) {
	start := time.Now()
	slog.Debug("Retrieving student by user ID", "user_id", userID)

	var student models.Student
	if err := p.db.Preload("User").
		Where("user_id = ?", userID).First(&student).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to retrieve student by user ID",
			"user_id", userID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, err
	}

	studentToReturn := &models.StudentForCreate{
		UserForCreate: models.UserForCreate{
			FullName:       student.User.FullName,
			Email:          student.User.Email,
			PhoneNumber:    student.User.PhoneNumber,
			ContactAddress: student.User.ContactAddress,
		},
		ParentPhone: student.ParentPhone,
		ParentEmail: student.ParentEmail,
		// Enhanced student information
		Institute:  student.Institute,
		Class:      student.Class,
		Stream:     student.Stream,
		CityOrTown: student.CityOrTown,
		State:      student.State,
	}

	duration := time.Since(start)
	slog.Debug("Student retrieved successfully",
		"user_id", userID,
		"email", student.User.Email,
		"duration_ms", duration.Milliseconds(),
	)
	return studentToReturn, nil
}

// GetStudents retrieves students with optional filters
func (p *DbPlugin) GetStudents(ctx context.Context, stream, class, name, email, phoneNumber, institution string) ([]models.StudentForGet, error) {
	start := time.Now()
	slog.Info("Retrieving students with filters",
		"stream", stream,
		"class", class,
		"name", name,
		"email", email,
		"phone_number", phoneNumber,
		"institution", institution,
	)

	var students []models.Student
	query := p.db.Preload("User")

	// Apply stream filter
	if stream != "" {
		query = query.Where("stream = ?", stream)
		slog.Debug("Applied stream filter", "stream", stream)
	}

	// Apply class filter
	if class != "" {
		query = query.Where("class = ?", class)
		slog.Debug("Applied class filter", "class", class)
	}

	// Apply name prefix filter (search in user's full_name)
	if name != "" {
		query = query.Joins("JOIN users ON users.id = students.user_id").
			Where("users.full_name ILIKE ?", name+"%")
		slog.Debug("Applied name prefix filter", "name", name)
	}

	// Apply email prefix filter
	if email != "" {
		if name == "" {
			// Only add JOIN if not already added by name filter
			query = query.Joins("JOIN users ON users.id = students.user_id")
		}
		query = query.Where("users.email ILIKE ?", email+"%")
		slog.Debug("Applied email prefix filter", "email", email)
	}

	// Apply phone number prefix filter
	if phoneNumber != "" {
		if name == "" && email == "" {
			// Only add JOIN if not already added by name or email filter
			query = query.Joins("JOIN users ON users.id = students.user_id")
		}
		query = query.Where("users.phone_number ILIKE ?", phoneNumber+"%")
		slog.Debug("Applied phone number prefix filter", "phone_number", phoneNumber)
	}

	// Apply institution exact match filter
	if institution != "" {
		query = query.Where("institute = ?", institution)
		slog.Debug("Applied institution exact match filter", "institution", institution)
	}

	// Order by created_at DESC to show newest students first
	query = query.Order("students.created_at DESC")

	if err := query.Find(&students).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to retrieve students",
			"stream", stream,
			"class", class,
			"name", name,
			"email", email,
			"phone_number", phoneNumber,
			"institution", institution,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("failed to retrieve students: %w", err)
	}

	// Convert to response format
	var studentsForList []models.StudentForGet
	for _, student := range students {
		studentsForList = append(studentsForList, models.StudentForGet{
			ID:          student.ID,
			FullName:    student.User.FullName,
			Email:       student.User.Email,
			PhoneNumber: student.User.PhoneNumber,
			ParentPhone: student.ParentPhone,
			ParentEmail: student.ParentEmail,
			Institute:   student.Institute,
			Class:       student.Class,
			Stream:      student.Stream,
			CityOrTown:  student.CityOrTown,
			State:       student.State,
			CreatedAt:   student.CreatedAt,
		})
	}

	duration := time.Since(start)
	slog.Info("Students retrieved successfully",
		"stream", stream,
		"class", class,
		"name", name,
		"email", email,
		"phone_number", phoneNumber,
		"institution", institution,
		"student_count", len(studentsForList),
		"duration_ms", duration.Milliseconds(),
	)

	return studentsForList, nil
}
