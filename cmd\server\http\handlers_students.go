package http

import (
	"fmt"
	"log/slog"
	"net/http"
	"os"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	twilio "github.com/twilio/twilio-go"
	verify "github.com/twilio/twilio-go/rest/verify/v2"

	"ziaacademy-backend/internal/models"
	"ziaacademy-backend/internal/token"
)

// CreateStudent godoc
//
//		@Summary		CreateStudent
//		@Description	create new student with enhanced information including institute, class, stream, city and state
//		@Description
//		@Description	Field Constraints:
//		@Description	- class: Must be one of '9th', '10th', '11th', '12th', 'dropper' (optional)
//		@Description	- stream: Must be one of 'IIT-JEE', 'NEET' (optional)
//		@Description	- email: Must be unique across all users
//		@Description	- phoneNumber: Must be unique across all users
//		@Description	- password: Must be at least 6 characters long (required)
//	 @Param			item	body	models.StudentForCreate	true	"student details with enhanced fields"
//		@Tags			students
//		@Accept			json
//		@Produce		json
//		@Success		200	{object}	models.CreatedStudentResponse
//		@Failure		400	{object}	HTTPError
//		@Failure		404	{object}	HTTPError
//		@Failure		500	{object}	HTTPError
//		@Router			/students [post]
func (h *Handlers) CreateStudent(ctx *gin.Context) {
	stuInput := new(models.StudentForCreate)
	if err := ctx.ShouldBindJSON(stuInput); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	slog.Debug("Processing student creation request", "email", stuInput.Email)
	// var stu models.Student
	stu := models.Student{
		User: models.User{
			FullName:       stuInput.FullName,
			Email:          stuInput.Email,
			PhoneNumber:    stuInput.PhoneNumber,
			ContactAddress: stuInput.ContactAddress,
			Role:           "Student",
			PasswordHash:   stuInput.Password, // This will be hashed in the database layer
		},
		ParentPhone: stuInput.ParentPhone,
		ParentEmail: stuInput.ParentEmail,
		Institute:   stuInput.Institute,
		Class:       stuInput.Class,
		Stream:      stuInput.Stream,
		CityOrTown:  stuInput.CityOrTown,
		State:       stuInput.State,
	}

	createdStudent, err := h.db.CreateStudent(ctx.Request.Context(), &stu)
	if err != nil {
		slog.Error("Failed to create student", "email", stuInput.Email,
			"error", err.Error())
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	slog.Debug("Created student", "email", createdStudent.User.Email)
	// Generate JWT token
	token, err := token.GenerateJWT(createdStudent.UserID)
	if err != nil {
		slog.Error("Failed to generate token", "email", stuInput.Email, "error", err.Error())
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate token"})
		return
	}

	slog.Debug("Generated token", "email", stuInput.Email)

	// Return the token and full student details
	response := models.CreatedStudentResponse{
		Token: token,
		Student: models.StudentForGet{
			UserForGet: models.UserForGet{
				ID:          createdStudent.ID,
				FullName:    createdStudent.User.FullName,
				Email:       createdStudent.User.Email,
				PhoneNumber: createdStudent.User.PhoneNumber,
				CreatedAt:   createdStudent.CreatedAt,
			},
			ParentPhone: createdStudent.ParentPhone,
			ParentEmail: createdStudent.ParentEmail,
			Institute:   createdStudent.Institute,
			Class:       createdStudent.Class,
			Stream:      createdStudent.Stream,
			CityOrTown:  createdStudent.CityOrTown,
			State:       createdStudent.State,
		},
	}
	ctx.JSON(http.StatusOK, response)
}

// SendVerificationCode godoc
//
//		@Summary		SendVerificationCode
//		@Description	send verification code to student via SMS or email
//		@Description
//		@Description	Field Constraints:
//		@Description	- phone_number: Must be a valid phone number
//	 @Param			item	body	models.SendVerificationCodeRequest	true	"verification code request details"
//		@Tags			students
//		@Accept			json
//		@Produce		json
//		@Success		200	{object}	models.SendVerificationCodeResponse
//		@Failure		400	{object}	HTTPError
//		@Failure		500	{object}	HTTPError
//		@Router			/students/send-verification-code [post]
//
// SendVerificationCode is the HTTP handler to send verification code to students
func (h *Handlers) SendVerificationCode(ctx *gin.Context) {
	start := time.Now()
	clientIP := ctx.ClientIP()

	var request models.SendVerificationCodeRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		duration := time.Since(start)
		slog.Warn("Send verification code failed - invalid request body",
			"client_ip", clientIP,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	slog.Info("Verification code request",
		"phone_number", request.PhoneNumber,
		"client_ip", clientIP,
	)

	accountSid := os.Getenv("TWILIO_ACCOUNT_SID")
	authToken := os.Getenv("TWILIO_AUTH_TOKEN")
	verifyServiceSid := os.Getenv("TWILIO_VERIFY_SERVICE_SID")
	slog.Info("Twilio credentials", "username", accountSid, "password", authToken)

	// Create a new Twilio client using the custom client
	client := twilio.NewRestClientWithParams(twilio.ClientParams{
		Username: accountSid,
		Password: authToken,
	})

	params := &verify.CreateVerificationParams{}
	params.SetTo(request.PhoneNumber)
	params.SetChannel("sms")

	resp, err := client.VerifyV2.CreateVerification(verifyServiceSid, params)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	} else {
		if resp.Sid != nil {
			fmt.Println(*resp.Sid)
		} else {
			fmt.Println(resp.Sid)
		}
	}

	duration := time.Since(start)
	slog.Info("Verification code sent successfully",
		"phone_number", request.PhoneNumber,
		"client_ip", clientIP,
		"duration_ms", duration.Milliseconds(),
	)

	response := models.SendVerificationCodeResponse{
		Message:   "Verification code sent successfully",
		CodeSent:  true,
		ExpiresAt: time.Now().Add(10 * time.Minute).Format(time.RFC3339),
	}

	ctx.JSON(http.StatusOK, response)
}

// VerifyCode godoc
//
//		@Summary		VerifyCode
//		@Description	verify the code entered by student
//		@Description
//		@Description	Field Constraints:
//		@Description	- phone_number: Must be a valid phone number
//		@Description	- code: Must be the verification code received via SMS
//	 @Param			item	body	models.VerifyCodeRequest	true	"verification code details"
//		@Tags			students
//		@Accept			json
//		@Produce		json
//		@Success		200	{object}	models.VerifyCodeResponse
//		@Failure		400	{object}	HTTPError
//		@Failure		500	{object}	HTTPError
//		@Router			/students/verify-code [post]
//
// VerifyCode is the HTTP handler to verify code entered by students
func (h *Handlers) VerifyCode(ctx *gin.Context) {
	start := time.Now()
	clientIP := ctx.ClientIP()

	var request models.VerifyCodeRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		duration := time.Since(start)
		slog.Warn("Verify code failed - invalid request body",
			"client_ip", clientIP,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	slog.Info("Verification code check request",
		"phone_number", request.PhoneNumber,
		"client_ip", clientIP,
	)

	accountSid := os.Getenv("TWILIO_ACCOUNT_SID")
	authToken := os.Getenv("TWILIO_AUTH_TOKEN")
	verifyServiceSid := os.Getenv("TWILIO_VERIFY_SERVICE_SID")
	slog.Debug("Twilio credentials", "username", accountSid, "password", authToken)

	// Create a new Twilio client using the custom client
	client := twilio.NewRestClientWithParams(twilio.ClientParams{
		Username: accountSid,
		Password: authToken,
	})

	params := &verify.CreateVerificationCheckParams{}
	params.SetTo(request.PhoneNumber)
	params.SetCode(request.Code)

	resp, err := client.VerifyV2.CreateVerificationCheck(verifyServiceSid, params)
	if err != nil {
		duration := time.Since(start)
		slog.Error("Verification check failed",
			"phone_number", request.PhoneNumber,
			"client_ip", clientIP,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	duration := time.Since(start)
	verified := false
	status := "pending"
	message := "Verification failed"

	if resp.Status != nil {
		status = *resp.Status
		switch status {
		case "approved":
			verified = true
			message = "Code verified successfully"
		case "pending":
			message = "Verification is pending"
		default:
			message = "Invalid verification code"
		}
	}

	slog.Info("Verification check completed",
		"phone_number", request.PhoneNumber,
		"verified", verified,
		"status", status,
		"client_ip", clientIP,
		"duration_ms", duration.Milliseconds(),
	)

	response := models.VerifyCodeResponse{
		Message:  message,
		Verified: verified,
		Status:   status,
	}

	ctx.JSON(http.StatusOK, response)
}

// EnrollInCourse godoc
//
//		@Summary		EnrollInCourse
//		@Description	enroll student in a course
//	     @Security       BearerAuth
//	 @Param			course_id	path	uint	true	"course ID to enroll in"
//		@Tags			students
//		@Accept			json
//		@Produce		json
//		@Success		200	{object}	models.Student
//		@Failure		400	{object}	HTTPError
//		@Failure		404	{object}	HTTPError
//		@Failure		500	{object}	HTTPError
//		@Router			/enroll/{course_id} [post]
func (h *Handlers) EnrollInCourse(ctx *gin.Context) {
	userID, err := token.ExtractTokenID(ctx)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	courseIDStr, ok := ctx.Params.Get("course_id")
	if !ok {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Missing course_id parameter"})
		return
	}
	courseID, _ := strconv.Atoi(courseIDStr)
	updatedStudent, err := h.db.EnrollStudentInCourse(ctx.Request.Context(),
		userID, uint(courseID))

	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	// Return the token to the user
	ctx.JSON(http.StatusOK, updatedStudent)
}

// GetStudents godoc
//
//	@Summary		Get Students
//	@Description	get list of all students with optional filters
//	@Description
//	@Description	Optional Filters:
//	@Description	- stream: Filter by stream ('IIT-JEE' or 'NEET')
//	@Description	- class: Filter by class ('9th', '10th', '11th', '12th', 'dropper')
//	@Description	- name: Prefix match on student's full name
//	@Description	- email: Prefix match on student's email
//	@Description	- institution: Exact match on student's institution
//	@Description	- phone_number: Prefix match on student's phone number
//	@Security       BearerAuth
//	@Param			stream			query		string	false	"Stream filter (IIT-JEE or NEET)"
//	@Param			class			query		string	false	"Class filter (9th, 10th, 11th, 12th, dropper)"
//	@Param			name			query		string	false	"Name prefix filter"
//	@Param			email			query		string	false	"Email prefix filter"
//	@Param			phone_number	query		string	false	"Phone number prefix filter"
//	@Param			institution		query		string	false	"Institution exact match filter"
//	@Tags			students
//	@Accept			json
//	@Produce		json
//	@Success		200	{object}	[]models.StudentForList
//	@Failure		400	{object}	HTTPError
//	@Failure		404	{object}	HTTPError
//	@Failure		500	{object}	HTTPError
//	@Router			/students [get]
func (h *Handlers) GetStudents(ctx *gin.Context) {
	start := time.Now()
	clientIP := ctx.ClientIP()

	// Extract query parameters
	stream := ctx.Query("stream")
	class := ctx.Query("class")
	name := ctx.Query("name")
	email := ctx.Query("email")
	phoneNumber := ctx.Query("phone_number")
	institution := ctx.Query("institution")

	slog.Info("GetStudents request started",
		"client_ip", clientIP,
		"stream", stream,
		"class", class,
		"name", name,
		"email", email,
		"phone_number", phoneNumber,
		"institution", institution,
	)

	// Validate stream parameter if provided
	if stream != "" && stream != "IIT-JEE" && stream != "NEET" {
		duration := time.Since(start)
		slog.Warn("GetStudents failed - invalid stream parameter",
			"client_ip", clientIP,
			"stream", stream,
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid stream parameter. Must be 'IIT-JEE' or 'NEET'"})
		return
	}

	// Validate class parameter if provided
	if class != "" {
		validClasses := []string{"9th", "10th", "11th", "12th", "dropper"}
		isValidClass := false
		for _, validClass := range validClasses {
			if class == validClass {
				isValidClass = true
				break
			}
		}
		if !isValidClass {
			duration := time.Since(start)
			slog.Warn("GetStudents failed - invalid class parameter",
				"client_ip", clientIP,
				"class", class,
				"duration_ms", duration.Milliseconds(),
			)
			ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid class parameter. Must be one of: 9th, 10th, 11th, 12th, dropper"})
			return
		}
	}

	students, err := h.db.GetStudents(ctx.Request.Context(), stream, class, name, email, phoneNumber, institution)
	if err != nil {
		duration := time.Since(start)
		slog.Error("GetStudents failed - database error",
			"client_ip", clientIP,
			"stream", stream,
			"class", class,
			"name", name,
			"email", email,
			"phone_number", phoneNumber,
			"institution", institution,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	duration := time.Since(start)
	slog.Info("GetStudents successful",
		"client_ip", clientIP,
		"stream", stream,
		"class", class,
		"name", name,
		"email", email,
		"phone_number", phoneNumber,
		"institution", institution,
		"student_count", len(students),
		"duration_ms", duration.Milliseconds(),
	)

	ctx.JSON(http.StatusOK, students)
}
